# Source-Inspired AI: Concrete Example

## Scenario: Knight vs Player in Cover

Let's see how both systems handle this realistic combat scenario:

**Situation:** 
- <PERSON> sees player
- Player runs behind a wall for cover
- <PERSON> loses sight, searches, then finds player again
- Player damages knight heavily
- <PERSON> needs to adapt

---

## Current BaseEnemy System

### Code Structure
```gdscript
# Simple state machine
enum State { IDLE, CHASE, ATTACK }
var current_state = State.IDLE

func _physics_process(delta):
    match current_state:
        State.IDLE:
            if can_see_player():
                current_state = State.CHASE
        State.CHASE:
            move_toward_player()
            if close_to_player():
                current_state = State.ATTACK
        State.ATTACK:
            attack_player()
            current_state = State.CHASE
```

### How It Handles the Scenario
```
1. Player spotted → Switch to CHASE
2. Player hides → Still in CHASE, walks into wall
3. Gets stuck at wall, can't find player
4. Player reappears → Back to normal chase
5. Takes heavy damage → No special reaction, continues attacking
```

**Problems:**
- ❌ Gets stuck when player hides
- ❌ No search behavior when loses sight
- ❌ Doesn't react to heavy damage
- ❌ Very predictable and "robotic"

---

## Source-Inspired System

### Code Structure
```gdscript
# Task-based system
enum Task {
    GET_PATH_TO_ENEMY,
    MOVE_TO_RANGE, 
    FACE_ENEMY,
    MELEE_ATTACK,
    SEARCH_LAST_KNOWN_POS,
    FIND_COVER,
    WAIT_FOR_HEAL
}

enum Condition {
    SEE_ENEMY,
    LOST_ENEMY, 
    HEAVY_DAMAGE,
    LOW_HEALTH,
    IN_ATTACK_RANGE
}

# Schedules define behavior sequences
var attack_schedule = [
    Task.GET_PATH_TO_ENEMY,
    Task.MOVE_TO_RANGE,
    Task.FACE_ENEMY, 
    Task.MELEE_ATTACK
]

var search_schedule = [
    Task.GET_PATH_TO_LAST_KNOWN_POS,
    Task.SEARCH_LAST_KNOWN_POS,
    Task.PATROL_AREA
]

var retreat_schedule = [
    Task.FIND_COVER,
    Task.MOVE_TO_COVER,
    Task.WAIT_FOR_HEAL
]
```

### How It Handles the Scenario
```
1. Player spotted → COND_SEE_ENEMY → Start attack_schedule
   - Task: GET_PATH_TO_ENEMY (finds route)
   - Task: MOVE_TO_RANGE (approaches player)

2. Player hides → COND_LOST_ENEMY interrupts attack_schedule
   - Switch to search_schedule
   - Task: GET_PATH_TO_LAST_KNOWN_POS (go where player was)
   - Task: SEARCH_LAST_KNOWN_POS (look around)

3. Player reappears → COND_SEE_ENEMY interrupts search_schedule  
   - Switch back to attack_schedule
   - Resume intelligent pursuit

4. Takes heavy damage → COND_HEAVY_DAMAGE interrupts attack_schedule
   - Switch to retreat_schedule  
   - Task: FIND_COVER (look for safe spot)
   - Task: MOVE_TO_COVER (retreat to safety)
   - Task: WAIT_FOR_HEAL (recover)

5. Health recovered → Resume attack_schedule
```

**Benefits:**
- ✅ Intelligently searches when loses sight
- ✅ Reacts to damage by retreating
- ✅ Smooth transitions between behaviors
- ✅ Feels like a smart opponent

---

## Code Comparison

### Current System (Fixed Behavior)
```gdscript
# This is all you get - rigid and predictable
func do_chase(delta):
    move_toward_player()
    if close_to_player():
        change_state(ATTACK)
    # No handling for: lost sight, heavy damage, obstacles, etc.
```

### Source-Inspired System (Adaptive Behavior)
```gdscript
# Tasks can be interrupted and rescheduled based on conditions
func execute_schedule():
    check_conditions()  # See what's happening in world
    
    if has_condition(COND_HEAVY_DAMAGE):
        interrupt_schedule()
        start_schedule(retreat_schedule)
    elif has_condition(COND_LOST_ENEMY):
        interrupt_schedule()  
        start_schedule(search_schedule)
    elif has_condition(COND_SEE_ENEMY):
        if current_schedule != attack_schedule:
            interrupt_schedule()
            start_schedule(attack_schedule)
    
    execute_current_task()
```

---

## Real-World Example: Smart Archer

### Current ArcherEnemy Behavior
```
"I see player → I shoot → I shoot → I shoot..."
```

### Source-Inspired Archer Behavior  
```
Combat Schedule:
1. TASK_FIND_SHOOTING_POSITION → Find good vantage point
2. TASK_MOVE_TO_POSITION → Get there safely  
3. TASK_AIM_AT_ENEMY → Line up shot
4. TASK_SHOOT_PROJECTILE → Fire!
5. TASK_ASSESS_EFFECTIVENESS → Did it work?

Interrupts:
- COND_PLAYER_IN_COVER → Switch to "Reposition Schedule"
- COND_BEING_FLANKED → Switch to "Retreat Schedule"  
- COND_ALLY_NEEDS_HELP → Switch to "Support Schedule"
- COND_OUT_OF_AMMO → Switch to "Reload Schedule"
```

**Result:** Archer that feels like a real opponent - repositions when you take cover, retreats when flanked, supports teammates, manages ammo intelligently.

---

## Implementation Complexity

### Development Time
- **Current System:** ✅ 1-2 hours to create new enemy
- **Source-Inspired:** ⚠️ 4-6 hours initial setup, then 2-3 hours per enemy

### Code Complexity  
- **Current System:** ✅ ~100 lines per enemy
- **Source-Inspired:** ⚠️ ~300 lines initial framework, ~150 lines per enemy

### Flexibility
- **Current System:** ⚠️ Hard to modify, lots of copy-paste
- **Source-Inspired:** ✅ Mix and match tasks infinitely, very DRY

### AI Quality
- **Current System:** ⚠️ Predictable, "videogame-y" behavior
- **Source-Inspired:** ✅ Dynamic, intelligent, "lifelike" behavior

---

## Recommendation

**For Prototyping:** Start with current BaseEnemy system
**For Production:** Upgrade to Source-inspired system

The Source-inspired system creates AI that players remember - enemies that feel intelligent and challenging rather than predictable patterns to exploit.