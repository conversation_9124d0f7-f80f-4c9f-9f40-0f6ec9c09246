extends BaseEnemy
class_name <PERSON><PERSON>nemy

# Ranged enemy implementation using BaseEnemy
# Shows how different the behavior can be while sharing the same foundation

@export_group("Archer Settings")
@export var preferred_distance: float = 8.0  # Stay back from player
@export var min_distance: float = 4.0        # Too close - retreat!
@export var projectile_scene: PackedScene    # Arrow/projectile to shoot
@export var projectile_speed: float = 20.0
@export var aim_time: float = 1.0            # Time to aim before shooting
@export var retreat_speed: float = 6.0       # Speed when backing away

# Archer-specific state
var aim_timer: float = 0.0
var is_aiming: bool = false
var is_retreating: bool = false

func setup_enemy():
	# Archer-specific setup
	if debug_enabled:
		print("[ArcherEnemy] Archer ready to shoot!")
	
	# Archers prefer to stay at range
	if not projectile_scene:
		push_warning("ArcherEnemy: No projectile_scene assigned! Archer can't shoot.")

# =============================================================================
# ARCHER-SPECIFIC STATE BEHAVIORS
# =============================================================================

func do_idle(delta: float):
	super.do_idle(delta)  # Call parent behavior
	
	# Archers are more alert - longer sight range
	if can_see_player() and target_player:
		var distance = global_position.distance_to(target_player.global_position)
		if distance <= sight_range:
			change_state(EnemyState.CHASE)

func enter_chase():
	super.enter_chase()  # Call parent behavior
	is_aiming = false
	is_retreating = false
	aim_timer = 0.0

func do_chase(delta: float):
	if not target_player:
		change_state(EnemyState.IDLE)
		return
	
	if not can_see_player():
		# Lost sight - use parent's lose target logic
		super.do_chase(delta)
		return
	
	var distance = global_position.distance_to(target_player.global_position)
	
	# Archer positioning logic
	if distance < min_distance:
		# Too close! Retreat
		retreat_from_player(delta)
	elif distance > preferred_distance + 2.0:
		# Too far! Move closer
		move_toward_position(target_player.global_position, movement_speed)
		face_position(target_player.global_position, delta)
	elif distance >= attack_range and attack_timer <= 0.0:
		# Perfect distance! Attack
		change_state(EnemyState.ATTACK)
	else:
		# Good position, just face player
		velocity.x = move_toward(velocity.x, 0, 5.0)
		velocity.z = move_toward(velocity.z, 0, 5.0)
		face_position(target_player.global_position, delta)

func retreat_from_player(delta: float):
	if not target_player:
		return
	
	is_retreating = true
	
	# Move away from player
	var direction = (global_position - target_player.global_position).normalized()
	direction.y = 0
	velocity.x = direction.x * retreat_speed
	velocity.z = direction.z * retreat_speed
	
	# Still face the player while retreating
	face_position(target_player.global_position, delta)
	
	if debug_enabled and not is_retreating:
		print("[ArcherEnemy] Retreating! Too close!")

func enter_attack():
	super.enter_attack()  # Call parent behavior
	is_aiming = true
	aim_timer = 0.0
	
	if debug_enabled:
		print("[ArcherEnemy] Taking aim...")

func do_attack(delta: float):
	# Stop moving and aim
	velocity.x = 0
	velocity.z = 0
	
	if not target_player:
		change_state(EnemyState.IDLE)
		return
	
	# Face target while aiming
	face_position(target_player.global_position, delta)
	
	# Aim time
	aim_timer += delta
	
	if aim_timer >= aim_time and attack_timer <= 0.0:
		# Fire!
		shoot_projectile()
		attack_timer = attack_cooldown
		change_state(EnemyState.CHASE)

func exit_attack():
	super.exit_attack()  # Call parent behavior
	is_aiming = false
	is_retreating = false

# =============================================================================
# ARCHER-SPECIFIC METHODS
# =============================================================================

func shoot_projectile():
	if not target_player or not projectile_scene:
		if debug_enabled:
			print("[ArcherEnemy] Can't shoot - no target or projectile!")
		return
	
	# Create projectile
	var projectile = projectile_scene.instantiate()
	get_tree().current_scene.add_child(projectile)
	
	# Position projectile at archer
	projectile.global_position = global_position + Vector3(0, 1.5, 0)  # Shoot from chest height
	
	# Calculate direction to player
	var direction = (target_player.global_position - projectile.global_position).normalized()
	
	# Set projectile velocity/direction (depends on your projectile implementation)
	if projectile.has_method("set_direction"):
		projectile.set_direction(direction, projectile_speed)
	elif projectile.has_method("set_velocity"):
		projectile.set_velocity(direction * projectile_speed)
	elif projectile is RigidBody3D:
		projectile.linear_velocity = direction * projectile_speed
	elif projectile is CharacterBody3D:
		projectile.velocity = direction * projectile_speed
	
	# Set damage if projectile supports it
	if projectile.has_method("set_damage"):
		projectile.set_damage(attack_damage)
	
	if debug_enabled:
		print("[ArcherEnemy] Arrow shot! Direction: %s" % direction)

# Override the base attack method since archers don't do melee
func perform_attack():
	# Archers use projectiles, not melee
	shoot_projectile()

# Override can_see_player for archer's better vision
func can_see_player() -> bool:
	if not target_player:
		return false
	
	var distance = global_position.distance_to(target_player.global_position)
	# Archers have better sight range
	if distance > sight_range * 1.2:
		return false
	
	# Same line of sight check as base class
	var space_state = get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(
		global_position + Vector3(0, 1.5, 0),  # Eye level
		target_player.global_position + Vector3(0, 1, 0)
	)
	query.exclude = [self]
	
	var result = space_state.intersect_ray(query)
	return result.is_empty() or result.collider == target_player

# Override take_damage for archer-specific behavior
func take_damage(amount: int):
	super.take_damage(amount)  # Call parent behavior
	
	# Archers panic when hurt - try to retreat
	if current_state != EnemyState.DEAD and target_player:
		var distance = global_position.distance_to(target_player.global_position)
		if distance < preferred_distance:
			if debug_enabled:
				print("[ArcherEnemy] Hit! Retreating!")
			# Force a retreat by changing state
			change_state(EnemyState.CHASE)
