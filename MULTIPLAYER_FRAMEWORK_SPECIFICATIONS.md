# Stiletto Proto - Multiplayer Framework Specifications

## Overview

This document outlines the complete specifications for a Source Engine-inspired multiplayer framework for Stiletto Proto, designed to support both competitive PvP and cooperative PvE gameplay modes.

## Table of Contents

1. [Core Architecture](#core-architecture)
2. [GameRulesManager System](#gamerules-manager-system)
3. [NetworkedPlayer Specifications](#networked-player-specifications)
4. [GameMode System](#gamemode-system)
5. [Server Authority & Lag Compensation](#server-authority--lag-compensation)
6. [Enemy AI Network Synchronization](#enemy-ai-network-synchronization)
7. [Team Management System](#team-management-system)
8. [Weapon Synchronization](#weapon-synchronization)
9. [Map/Level Management](#maplevel-management)
10. [Player Spawning System](#player-spawning-system)
11. [Game State Synchronization](#game-state-synchronization)
12. [Chat & Communication](#chat--communication)
13. [Match Management](#match-management)
14. [Admin Controls & Console Commands](#admin-controls--console-commands)
15. [Spectator System](#spectator-system)
16. [Damage & Health Synchronization](#damage--health-synchronization)
17. [Anti-Cheat Foundation](#anti-cheat-foundation)
18. [Testing Framework](#testing-framework)
19. [Configuration Management](#configuration-management)

---

## Core Architecture

### Framework Structure
```
res://multiplayer_framework/
├── core/
│   ├── game_rules_manager.gd
│   ├── networked_player_manager.gd
│   ├── team_manager.gd
│   └── multiplayer_framework.gd
├── game_modes/
│   ├── base_game_mode.gd
│   ├── deathmatch_mode.gd
│   ├── coop_mode.gd
│   ├── team_deathmatch_mode.gd
│   └── hybrid_mode.gd
├── networking/
│   ├── lag_compensation.gd
│   ├── server_authority.gd
│   └── network_sync.gd
├── player/
│   ├── networked_player.gd
│   ├── player_states.gd
│   └── player_input_buffer.gd
├── systems/
│   ├── spawn_manager.gd
│   ├── chat_manager.gd
│   ├── match_manager.gd
│   └── spectator_manager.gd
└── utils/
    ├── console_commands.gd
    ├── anti_cheat.gd
    └── config_manager.gd
```

### Integration Points with Existing Systems
- **GoldGdt Player**: Extend with NetworkedPlayer wrapper
- **BaseEnemy**: Add NetworkedEnemy synchronization layer
- **WeaponManager**: Integrate with server authority system
- **Current MultiplayerManager**: Evolve into core framework controller

---

## GameRulesManager System

### Core Responsibilities
- Central authority for all game logic and rules
- Mode switching and configuration
- Score tracking and win conditions
- Player event handling (kills, deaths, objectives)
- Map rotation and time limits

### Class Structure
```gdscript
class_name GameRulesManager extends Node

# Core Properties
var current_game_mode: BaseGameMode
var match_state: MatchState
var game_config: GameConfiguration
var player_scores: Dictionary
var team_scores: Dictionary

# Game States
enum MatchState {
    WAITING_FOR_PLAYERS,
    STARTING,
    IN_PROGRESS, 
    INTERMISSION,
    MAP_CHANGING
}

# Event System
signal player_killed(victim: NetworkedPlayer, killer: NetworkedPlayer)
signal player_spawned(player: NetworkedPlayer)
signal match_started()
signal match_ended(winning_team: int)
signal round_started()
signal round_ended()
```

### Game Configuration
```gdscript
class_name GameConfiguration extends Resource

@export var mp_teamplay: bool = false
@export var mp_fraglimit: int = 0
@export var mp_timelimit: float = 0.0
@export var mp_respawn_delay: float = 3.0
@export var mp_friendly_fire: bool = false
@export var mp_max_players: int = 8
@export var mp_map_rotation: Array[String] = []
```

### Key Methods
- `set_game_mode(mode: BaseGameMode)`
- `handle_player_death(victim, killer, damage_info)`
- `check_win_conditions() -> bool`
- `start_match()`
- `end_match()`
- `get_player_score(player_id: int) -> int`
- `get_team_score(team: int) -> int`

---

## NetworkedPlayer Specifications

### Core Design
Extends existing GoldGdt player system with network synchronization and server authority.

### Class Structure
```gdscript
class_name NetworkedPlayer extends Node3D

# Core Components
var goldgdt_pawn: GoldGdt_Pawn
var player_state: PlayerState
var input_buffer: PlayerInputBuffer
var lag_compensation: LagCompensationData

# Network Properties
@export var peer_id: int
@export var player_name: String
@export var team: int = 0
@export var is_ready: bool = false

# Synchronized Properties
var health: int = 100
var armor: int = 0
var score: int = 0
var deaths: int = 0
var kills: int = 0

# Player States
enum PlayerState {
    ACTIVE,
    SPECTATING,
    DEAD,
    CONNECTING,
    DISCONNECTED
}
```

### Network Synchronization
- **Position**: Client prediction with server correction
- **Health/Armor**: Server authoritative
- **Weapons**: Server validates, client predicts
- **Animation**: Client side with sync points

### Integration with GoldGdt
```gdscript
func _ready():
    # Get existing GoldGdt pawn
    goldgdt_pawn = get_node("GoldGdtPawn")
    
    # Hook into movement system
    goldgdt_pawn.connect("position_changed", _on_position_changed)
    
    # Setup network sync
    if multiplayer.is_server():
        setup_server_authority()
    else:
        setup_client_prediction()
```

---

## GameMode System

### Base GameMode
```gdscript
class_name BaseGameMode extends Node

# Core Properties
@export var mode_name: String
@export var supports_teams: bool = false
@export var supports_ai_enemies: bool = false
@export var respawn_delay: float = 3.0
@export var match_time_limit: float = 0.0

# Virtual Methods
func setup_mode() -> void: pass
func player_spawned(player: NetworkedPlayer) -> void: pass
func player_killed(victim: NetworkedPlayer, killer: NetworkedPlayer) -> void: pass
func check_win_condition() -> bool: return false
func get_spawn_point(player: NetworkedPlayer) -> Vector3: return Vector3.ZERO
```

### Specific Game Modes

#### DeathMatchMode
- Free-for-all combat
- Score-based win conditions
- Individual player spawning
- Weapon pickup system

#### CoopMode
- PvE focused gameplay
- Shared objectives
- AI enemy management
- Drop-in/drop-out support

#### TeamDeathMatchMode
- Team-based combat
- Team scoring system
- Team-balanced spawning
- Team communication

#### HybridMode
- Combines PvP and PvE elements
- Dynamic mode switching
- Faction vs AI vs Players
- Complex win conditions

---

## Server Authority & Lag Compensation

### Server Authority System
```gdscript
class_name ServerAuthority extends Node

# Authoritative Systems
var player_positions: Dictionary
var player_health: Dictionary
var weapon_states: Dictionary
var game_events: Array

# Validation Methods
func validate_player_movement(player_id: int, position: Vector3, timestamp: float) -> bool
func validate_weapon_fire(player_id: int, weapon_id: int, target: Vector3) -> bool
func validate_damage_dealt(attacker_id: int, victim_id: int, damage: int) -> bool
```

### Lag Compensation
```gdscript
class_name LagCompensation extends Node

# Historical Data
var player_history: Dictionary  # [player_id][timestamp] = position_data
var max_history_time: float = 1.0  # Keep 1 second of history

# Core Methods
func rewind_players(timestamp: float)
func restore_players()
func get_player_position_at_time(player_id: int, timestamp: float) -> Vector3
func validate_hit(shooter_id: int, target_id: int, timestamp: float) -> bool
```

### Network Optimization
- Delta compression for position updates
- Selective entity updates based on relevance
- Bandwidth throttling for different connection types
- Packet loss handling and recovery

---

## Enemy AI Network Synchronization

### NetworkedEnemy Extension
```gdscript
class_name NetworkedEnemy extends BaseEnemy

# Network Properties
var network_id: int
var last_sync_position: Vector3
var last_sync_state: EnemyState
var authority_peer: int  # Which peer controls this enemy

# Synchronization
func sync_enemy_state():
    if multiplayer.is_server():
        _rpc_sync_enemy_state.rpc(global_position, current_state, health)

@rpc("authority", "call_remote", "unreliable")
func _rpc_sync_enemy_state(position: Vector3, state: EnemyState, hp: int):
    if not multiplayer.is_server():
        global_position = position
        current_state = state
        health = hp
```

### AI Distribution System
- Server manages AI state machine logic
- Clients receive position and animation updates
- Damage validation on server
- Load balancing for large numbers of enemies

### Enemy Spawning
- Server-controlled spawn points
- Dynamic difficulty scaling
- Wave management system
- Synchronized enemy objectives

---

## Team Management System

### Team Structure
```gdscript
class_name TeamManager extends Node

# Team Data
enum Team { UNASSIGNED = 0, TEAM_1 = 1, TEAM_2 = 2, SPECTATOR = 3 }

var teams: Dictionary = {
    Team.TEAM_1: TeamData.new("Team 1", Color.BLUE),
    Team.TEAM_2: TeamData.new("Team 2", Color.RED)
}

class TeamData:
    var name: String
    var color: Color
    var score: int = 0
    var players: Array[NetworkedPlayer] = []
    
    func _init(team_name: String, team_color: Color):
        name = team_name
        color = team_color
```

### Team Balancing
- Auto-balance on player join
- Skill-based team assignment
- Protection against team stacking
- Mid-game balance enforcement

### Team Communication
- Team-only chat channels
- Voice communication support
- Team-specific UI elements
- Shared objectives and scoring

---

## Weapon Synchronization

### Enhanced Weapon System
```gdscript
class_name NetworkedWeapon extends BaseWeapon

# Network Properties
var last_fire_timestamp: float
var server_ammo_count: int
var prediction_errors: int

# Client Prediction
func fire_weapon():
    if multiplayer.is_server():
        # Server authoritative fire
        if can_fire():
            _perform_fire()
            _rpc_weapon_fired.rpc(global_position, get_fire_direction())
    else:
        # Client prediction
        if can_fire_predicted():
            _predict_fire()
            _rpc_request_fire.rpc_id(1, global_position, get_fire_direction())

# Server Validation
@rpc("any_peer", "call_remote", "reliable")
func _rpc_request_fire(fire_pos: Vector3, fire_dir: Vector3):
    if multiplayer.is_server():
        if validate_fire_request(multiplayer.get_remote_sender_id(), fire_pos, fire_dir):
            _perform_fire()
            _rpc_weapon_fired.rpc(fire_pos, fire_dir)
        else:
            _rpc_fire_rejected.rpc_id(multiplayer.get_remote_sender_id())
```

### Weapon Management
- Server-validated ammunition counts
- Weapon pickup synchronization
- Weapon switching prediction
- Reload state synchronization

---

## Map/Level Management

### Map System
```gdscript
class_name MapManager extends Node

# Map Data
var current_map: MapInfo
var map_rotation: Array[MapInfo]
var next_map_index: int = 0

class MapInfo extends Resource:
    @export var map_name: String
    @export var scene_path: String
    @export var supported_modes: Array[String]
    @export var max_players: int
    @export var spawn_points: Array[Vector3]
```

### Map Rotation
- Automatic map changes after matches
- Voting system for map selection
- Mode-specific map filtering
- Custom map support

### Level Loading
- Seamless map transitions
- Progressive loading for large maps
- Asset preloading and caching
- Cross-platform compatibility

---

## Player Spawning System

### Spawn Manager
```gdscript
class_name SpawnManager extends Node

# Spawn Point Management
var spawn_points: Dictionary  # [team] = Array[SpawnPoint]
var last_spawn_times: Dictionary  # [spawn_point] = timestamp
var spawn_protection_time: float = 3.0

class SpawnPoint extends Node3D:
    @export var team: int = 0
    @export var enabled: bool = true
    @export var protection_radius: float = 5.0
    
    func is_safe_to_spawn() -> bool:
        # Check for nearby enemies
        # Check for ongoing combat
        # Validate spawn protection
```

### Spawn Logic
- Intelligent spawn point selection
- Spawn protection system
- Team-based spawn areas
- Anti-camping measures

### Respawn System
- Configurable respawn delays
- Wave-based respawning
- Spectator cam during respawn
- Loadout selection

---

## Game State Synchronization

### State Management
```gdscript
class_name GameStateSynchronizer extends Node

# Synchronized State
var authoritative_state: GameState
var client_states: Dictionary  # [peer_id] = ClientState

class GameState:
    var match_time: float
    var match_state: MatchState
    var player_data: Dictionary
    var team_scores: Dictionary
    var current_objectives: Array
```

### Synchronization Methods
- Delta compression for state updates
- Priority-based update scheduling
- Conflict resolution protocols
- State rollback capabilities

---

## Chat & Communication

### Chat System
```gdscript
class_name ChatManager extends Node

# Chat Channels
enum ChatChannel { ALL, TEAM, PRIVATE, ADMIN }

# Message Management
func send_message(sender_id: int, channel: ChatChannel, message: String, target_id: int = -1)
func broadcast_system_message(message: String)
func mute_player(player_id: int, duration: float = -1)

# Moderation
var muted_players: Dictionary
var chat_filters: Array[String]
var spam_detection: Dictionary
```

### Features
- Channel-based messaging
- Player muting and moderation
- Chat history and logging
- Spam prevention
- Rich text support

---

## Match Management

### Match Controller
```gdscript
class_name MatchManager extends Node

# Match Flow
enum MatchPhase { WARMUP, LIVE, OVERTIME, ENDED }

var current_phase: MatchPhase
var match_duration: float
var round_time: float
var intermission_time: float = 15.0

# Match Events
func start_match()
func end_match()
func start_round()
func end_round()
func enter_intermission()
```

### Match Features
- Pre-match warmup period
- Round-based gameplay support
- Overtime handling
- Match statistics collection
- Replay system foundation

---

## Admin Controls & Console Commands

### Console System
```gdscript
class_name ConsoleCommands extends Node

# Command Registration
var commands: Dictionary = {}

func register_command(name: String, callback: Callable, description: String)
func execute_command(command: String, args: Array, executor_id: int) -> String

# Built-in Commands
func cmd_kick(player_id: int)
func cmd_ban(player_id: int, duration: float = -1)
func cmd_changemap(map_name: String)
func cmd_set_gamemode(mode_name: String)
```

### Admin Features
- Permission-based command access
- Remote administration
- Server configuration
- Player management tools
- Debug commands

---

## Spectator System

### Spectator Management
```gdscript
class_name SpectatorManager extends Node

# Spectator Modes
enum SpectatorMode { FREE_CAM, FOLLOW_PLAYER, FIXED_CAM }

# Spectator Data
var spectators: Array[NetworkedPlayer]
var spectator_targets: Dictionary  # [spectator_id] = target_player

# Controls
func enter_spectator_mode(player: NetworkedPlayer)
func exit_spectator_mode(player: NetworkedPlayer)
func set_spectator_target(spectator: NetworkedPlayer, target: NetworkedPlayer)
```

### Features
- Multiple camera modes
- Player following
- Team-only spectating
- Spectator chat channel
- Match observation tools

---

## Damage & Health Synchronization

### Damage System
```gdscript
class_name DamageManager extends Node

# Damage Processing
func apply_damage(victim_id: int, attacker_id: int, damage: int, damage_type: String)
func validate_damage(damage_info: DamageInfo) -> bool
func calculate_damage_falloff(base_damage: int, distance: float) -> int

class DamageInfo:
    var victim_id: int
    var attacker_id: int
    var damage: int
    var damage_type: String
    var hit_location: String
    var timestamp: float
```

### Health Management
- Server-authoritative health
- Damage validation and anti-cheat
- Healing system support
- Armor and protection calculations
- Death and respawn handling

---

## Anti-Cheat Foundation

### Validation Systems
```gdscript
class_name AntiCheatManager extends Node

# Validation Checks
func validate_player_movement(player_id: int, old_pos: Vector3, new_pos: Vector3, delta: float) -> bool
func validate_weapon_timing(player_id: int, weapon_id: int, fire_time: float) -> bool
func detect_impossible_shots(shooter_id: int, target_id: int, hit_pos: Vector3) -> bool

# Detection Systems
var speed_violations: Dictionary
var impossible_shots: Dictionary
var suspicious_activity: Dictionary
```

### Security Features
- Movement validation
- Weapon timing checks
- Impossible shot detection
- Player behavior analysis
- Automated response system

---

## Testing Framework

### Test Structure
```gdscript
class_name MultiplayerTestSuite extends Node

# Test Categories
func test_networking()
func test_game_modes()
func test_player_synchronization()
func test_weapon_systems()
func test_ai_synchronization()

# Automated Testing
func run_stress_test(player_count: int, duration: float)
func simulate_network_conditions(latency: float, packet_loss: float)
```

### Testing Features
- Automated test bots
- Network simulation
- Performance benchmarking
- Integration testing
- Regression testing

---

## Configuration Management

### Config System
```gdscript
class_name ConfigManager extends Node

# Configuration Categories
var server_config: ServerConfig
var gameplay_config: GameplayConfig
var network_config: NetworkConfig

# Loading and Saving
func load_config(file_path: String) -> bool
func save_config(file_path: String) -> bool
func reset_to_defaults()
```

### Configuration Features
- Server-side configuration
- Runtime config changes
- Config validation
- Backup and restore
- Remote configuration

---

## Implementation Priority

### Phase 1 - Core Framework
1. GameRulesManager
2. NetworkedPlayer
3. Server Authority
4. Basic Game Modes

### Phase 2 - Enhanced Features
1. Team Management
2. Weapon Synchronization
3. Enemy AI Networking
4. Chat System

### Phase 3 - Advanced Systems
1. Lag Compensation
2. Anti-Cheat
3. Spectator System
4. Admin Tools

### Phase 4 - Polish & Testing
1. Testing Framework
2. Performance Optimization
3. Documentation
4. Configuration Management

---

## Integration Guidelines

### With Existing Systems
- Maintain compatibility with current BaseEnemy AI
- Extend GoldGdt player system without breaking changes
- Enhance current MultiplayerManager gradually
- Preserve existing weapon system functionality

### Development Approach
- Build incrementally with working prototypes
- Test each component independently
- Maintain backward compatibility
- Document all interfaces and APIs

This specification provides the complete blueprint for implementing a professional-grade multiplayer framework that supports both competitive PvP and cooperative PvE gameplay while maintaining compatibility with your existing systems.