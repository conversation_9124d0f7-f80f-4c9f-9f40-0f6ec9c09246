extends CharacterBody3D
class_name BaseEnemy

# Base enemy state machine system
# Provides common functionality that all enemies can use

enum EnemyState {
	IDLE,
	PATROL, 
	CHASE,
	ATTACK,
	STUNNED,
	DEAD
}

# Core properties - easily configurable in editor
@export_group("Movement")
@export var movement_speed: float = 5.0
@export var patrol_speed: float = 2.5
@export var turn_speed: float = 5.0

@export_group("Combat")
@export var attack_range: float = 2.0
@export var attack_damage: int = 25
@export var attack_cooldown: float = 1.0

@export_group("Detection")
@export var sight_range: float = 10.0
@export var hearing_range: float = 5.0
@export var lose_target_time: float = 3.0

@export_group("Health")
@export var max_health: int = 100

@export_group("Debug")
@export var debug_enabled: bool = false
@export var show_state_label: bool = true

# Internal state
var current_state: EnemyState = EnemyState.IDLE
var previous_state: EnemyState = EnemyState.IDLE
var state_timer: float = 0.0
var health: int
var target_player: Node3D
var last_known_player_pos: Vector3
var attack_timer: float = 0.0
var lose_target_timer: float = 0.0

# Optional nodes - will be found automatically if they exist
@onready var navigation_agent: NavigationAgent3D = get_node_or_null("NavigationAgent3D")
@onready var state_label: Label3D = get_node_or_null("StateLabel")

# Gravity
var gravity: float = ProjectSettings.get_setting("physics/3d/default_gravity", 9.8)

func _ready():
	# Initialize
	health = max_health
	add_to_group("enemies")
	
	# Find player
	_find_player()
	
	# Setup navigation if available
	if navigation_agent:
		navigation_agent.max_speed = movement_speed
		navigation_agent.path_desired_distance = 1.0
		navigation_agent.target_desired_distance = 1.5
	
	# Setup debug label
	if state_label and show_state_label:
		state_label.visible = true
	elif state_label:
		state_label.visible = false
	
	# Call child setup
	setup_enemy()

func _physics_process(delta):
	if current_state == EnemyState.DEAD:
		return
	
	# Update timers
	state_timer += delta
	attack_timer = max(0.0, attack_timer - delta)
	
	# Apply gravity
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	# Update player detection
	_update_player_detection(delta)
	
	# Execute current state
	_execute_state(delta)
	
	# Update debug display
	_update_debug_display()
	
	# Move the character
	move_and_slide()

# =============================================================================
# STATE MACHINE CORE
# =============================================================================

func change_state(new_state: EnemyState):
	if new_state == current_state:
		return
	
	# Exit current state
	_exit_state(current_state)
	
	# Change state
	previous_state = current_state
	current_state = new_state
	state_timer = 0.0
	
	# Enter new state
	_enter_state(new_state)
	
	if debug_enabled:
		print("[%s] State: %s -> %s" % [name, EnemyState.keys()[previous_state], EnemyState.keys()[current_state]])

func _execute_state(delta: float):
	match current_state:
		EnemyState.IDLE:
			do_idle(delta)
		EnemyState.PATROL:
			do_patrol(delta)
		EnemyState.CHASE:
			do_chase(delta)
		EnemyState.ATTACK:
			do_attack(delta)
		EnemyState.STUNNED:
			do_stunned(delta)
		EnemyState.DEAD:
			do_dead(delta)

func _enter_state(state: EnemyState):
	match state:
		EnemyState.IDLE:
			enter_idle()
		EnemyState.PATROL:
			enter_patrol()
		EnemyState.CHASE:
			enter_chase()
		EnemyState.ATTACK:
			enter_attack()
		EnemyState.STUNNED:
			enter_stunned()
		EnemyState.DEAD:
			enter_dead()

func _exit_state(state: EnemyState):
	match state:
		EnemyState.IDLE:
			exit_idle()
		EnemyState.PATROL:
			exit_patrol()
		EnemyState.CHASE:
			exit_chase()
		EnemyState.ATTACK:
			exit_attack()
		EnemyState.STUNNED:
			exit_stunned()
		EnemyState.DEAD:
			exit_dead()

# =============================================================================
# OVERRIDABLE STATE METHODS - Child classes customize these
# =============================================================================

# Called once when enemy is ready
func setup_enemy():
	pass

# IDLE state
func enter_idle(): pass
func do_idle(delta: float):
	velocity.x = move_toward(velocity.x, 0, 5.0)
	velocity.z = move_toward(velocity.z, 0, 5.0)
	
	# Basic behavior: switch to chase if player is visible
	if can_see_player():
		change_state(EnemyState.CHASE)
func exit_idle(): pass

# PATROL state  
func enter_patrol(): pass
func do_patrol(delta: float):
	# Default: just idle (child classes override for actual patrol)
	do_idle(delta)
func exit_patrol(): pass

# CHASE state
func enter_chase():
	if target_player:
		last_known_player_pos = target_player.global_position
func do_chase(delta: float):
	if not target_player:
		change_state(EnemyState.IDLE)
		return
	
	# Move toward player
	var target_pos = target_player.global_position if can_see_player() else last_known_player_pos
	move_toward_position(target_pos, movement_speed)
	face_position(target_pos, delta)
	
	# Check for attack range
	var distance = global_position.distance_to(target_player.global_position)
	if distance <= attack_range and attack_timer <= 0.0:
		change_state(EnemyState.ATTACK)
	
	# Check if lost player
	if not can_see_player():
		lose_target_timer += delta
		if lose_target_timer >= lose_target_time:
			change_state(EnemyState.IDLE)
	else:
		lose_target_timer = 0.0
		last_known_player_pos = target_player.global_position
func exit_chase(): pass

# ATTACK state
func enter_attack(): pass
func do_attack(delta: float):
	# Face target and stop moving
	velocity.x = 0
	velocity.z = 0
	
	if target_player:
		face_position(target_player.global_position, delta)
		
		# Perform attack
		if state_timer >= 0.3 and attack_timer <= 0.0:  # Attack after brief windup
			perform_attack()
			attack_timer = attack_cooldown
			change_state(EnemyState.CHASE)
	else:
		change_state(EnemyState.IDLE)
func exit_attack(): pass

# STUNNED state
func enter_stunned(): pass
func do_stunned(delta: float):
	velocity.x = move_toward(velocity.x, 0, 8.0)
	velocity.z = move_toward(velocity.z, 0, 8.0)
func exit_stunned(): pass

# DEAD state
func enter_dead():
	set_collision_layer_value(1, false)  # Remove from physics
func do_dead(delta: float):
	velocity.x = move_toward(velocity.x, 0, 2.0)
	velocity.z = move_toward(velocity.z, 0, 2.0)
func exit_dead(): pass

# =============================================================================
# UTILITY METHODS
# =============================================================================

func _find_player():
	var players = get_tree().get_nodes_in_group("player")
	if players.size() > 0:
		target_player = players[0]

func _update_player_detection(delta: float):
	if not target_player:
		_find_player()

func can_see_player() -> bool:
	if not target_player:
		return false
	
	var distance = global_position.distance_to(target_player.global_position)
	if distance > sight_range:
		return false
	
	# Simple line of sight check
	var space_state = get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(
		global_position + Vector3(0, 1, 0),
		target_player.global_position + Vector3(0, 1, 0)
	)
	query.exclude = [self]
	
	var result = space_state.intersect_ray(query)
	return result.is_empty() or result.collider == target_player

func move_toward_position(target_pos: Vector3, speed: float):
	if navigation_agent and navigation_agent.is_navigation_finished() == false:
		# Use navigation
		navigation_agent.target_position = target_pos
		var next_pos = navigation_agent.get_next_path_position()
		var direction = (next_pos - global_position).normalized()
		direction.y = 0
		velocity.x = direction.x * speed
		velocity.z = direction.z * speed
	else:
		# Direct movement
		var direction = (target_pos - global_position).normalized()
		direction.y = 0
		velocity.x = direction.x * speed
		velocity.z = direction.z * speed

func face_position(target_pos: Vector3, delta: float):
	var direction = (target_pos - global_position).normalized()
	direction.y = 0
	if direction.length() > 0:
		var target_transform = transform.looking_at(global_position + direction, Vector3.UP)
		transform = transform.interpolate_with(target_transform, turn_speed * delta)

func perform_attack():
	if not target_player:
		return
	
	var distance = global_position.distance_to(target_player.global_position)
	if distance <= attack_range:
		if target_player.has_method("take_damage"):
			target_player.take_damage(attack_damage)
		
		if debug_enabled:
			print("[%s] Attack! Damage: %d" % [name, attack_damage])

func take_damage(amount: int):
	if current_state == EnemyState.DEAD:
		return
	
	health -= amount
	
	if debug_enabled:
		print("[%s] Took %d damage, health: %d" % [name, amount, health])
	
	if health <= 0:
		change_state(EnemyState.DEAD)
	elif current_state != EnemyState.STUNNED:
		# Brief stun when taking damage
		change_state(EnemyState.STUNNED)
		await get_tree().create_timer(0.3).timeout
		if current_state == EnemyState.STUNNED:
			change_state(EnemyState.CHASE if target_player else EnemyState.IDLE)

func _update_debug_display():
	if state_label and show_state_label:
		state_label.text = EnemyState.keys()[current_state]
		# Color code states
		match current_state:
			EnemyState.IDLE:
				state_label.modulate = Color.WHITE
			EnemyState.PATROL:
				state_label.modulate = Color.BLUE
			EnemyState.CHASE:
				state_label.modulate = Color.YELLOW
			EnemyState.ATTACK:
				state_label.modulate = Color.RED
			EnemyState.STUNNED:
				state_label.modulate = Color.ORANGE
			EnemyState.DEAD:
				state_label.modulate = Color.GRAY