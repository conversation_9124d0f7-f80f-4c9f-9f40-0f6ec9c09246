[gd_scene load_steps=4 format=3 uid="uid://b12oujvp5ujyr"]

[ext_resource type="Script" uid="uid://25rcjckiqdik" path="res://scripts/enemy/ArcherEnemy.gd" id="1_m0iv2"]

[sub_resource type="CylinderMesh" id="CylinderMesh_m0iv2"]
top_radius = 0.3
height = 1.8

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_mqs3d"]
radius = 0.4
height = 1.8

[node name="ArcherEnemy" type="CharacterBody3D"]
script = ExtResource("1_m0iv2")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("CylinderMesh_m0iv2")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("CapsuleShape3D_mqs3d")

[node name="NavigationAgent3D" type="NavigationAgent3D" parent="."]

[node name="StateLabel" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.2, 0)
billboard = 1
text = "IDLE"
